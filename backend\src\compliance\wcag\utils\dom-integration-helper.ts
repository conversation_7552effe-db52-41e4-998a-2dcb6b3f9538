/**
 * DOM Integration Helper
 * Provides utilities to help WCAG checks integrate with the unified DOM extractor
 * Replaces old page.evaluate() patterns with unified structure access
 */

import { Page } from 'puppeteer';
import { PageStructure, ImageElement, FormElement, LinkElement, HeadingElement } from './unified-dom-extractor';
import { CheckConfig } from './check-template';
import logger from '../../../utils/logger';

export interface DOMIntegrationConfig {
  requiresPageStructure: boolean;
  fallbackToDirectExtraction: boolean;
  logMissingStructure: boolean;
}

/**
 * DOM Integration Helper - Assists checks in using unified DOM structure
 */
export class DOMIntegrationHelper {
  private static instance: DOMIntegrationHelper;

  static getInstance(): DOMIntegrationHelper {
    if (!DOMIntegrationHelper.instance) {
      DOMIntegrationHelper.instance = new DOMIntegrationHelper();
    }
    return DOMIntegrationHelper.instance;
  }

  /**
   * Get page structure with validation and fallback
   */
  getPageStructure(
    config: CheckConfig,
    integrationConfig: DOMIntegrationConfig = this.getDefaultConfig()
  ): PageStructure | null {
    if (!config.pageStructure) {
      if (integrationConfig.logMissingStructure) {
        logger.warn(`⚠️ [${config.scanId}] Page structure not available - unified DOM extractor not integrated`);
      }
      
      if (integrationConfig.requiresPageStructure) {
        throw new Error('Page structure required but not available - check unified DOM extractor integration');
      }
      
      return null;
    }

    return config.pageStructure;
  }

  /**
   * Get images from unified structure with fallback
   */
  async getImages(
    config: CheckConfig,
    page?: Page,
    integrationConfig: DOMIntegrationConfig = this.getDefaultConfig()
  ): Promise<ImageElement[]> {
    const pageStructure = this.getPageStructure(config, integrationConfig);
    
    if (pageStructure) {
      logger.debug(`📋 Using unified DOM structure: ${pageStructure.elements.images.length} images found`);
      return pageStructure.elements.images;
    }

    // Fallback to direct extraction if enabled
    if (integrationConfig.fallbackToDirectExtraction && page) {
      logger.debug('📋 Falling back to direct image extraction');
      return await this.extractImagesDirectly(page);
    }

    return [];
  }

  /**
   * Get forms from unified structure with fallback
   */
  async getForms(
    config: CheckConfig,
    page?: Page,
    integrationConfig: DOMIntegrationConfig = this.getDefaultConfig()
  ): Promise<FormElement[]> {
    const pageStructure = this.getPageStructure(config, integrationConfig);
    
    if (pageStructure) {
      logger.debug(`📋 Using unified DOM structure: ${pageStructure.elements.forms.length} forms found`);
      return pageStructure.elements.forms;
    }

    // Fallback to direct extraction if enabled
    if (integrationConfig.fallbackToDirectExtraction && page) {
      logger.debug('📋 Falling back to direct form extraction');
      return await this.extractFormsDirectly(page);
    }

    return [];
  }

  /**
   * Get links from unified structure with fallback
   */
  async getLinks(
    config: CheckConfig,
    page?: Page,
    integrationConfig: DOMIntegrationConfig = this.getDefaultConfig()
  ): Promise<LinkElement[]> {
    const pageStructure = this.getPageStructure(config, integrationConfig);
    
    if (pageStructure) {
      logger.debug(`📋 Using unified DOM structure: ${pageStructure.elements.links.length} links found`);
      return pageStructure.elements.links;
    }

    // Fallback to direct extraction if enabled
    if (integrationConfig.fallbackToDirectExtraction && page) {
      logger.debug('📋 Falling back to direct link extraction');
      return await this.extractLinksDirectly(page);
    }

    return [];
  }

  /**
   * Get headings from unified structure with fallback
   */
  async getHeadings(
    config: CheckConfig,
    page?: Page,
    integrationConfig: DOMIntegrationConfig = this.getDefaultConfig()
  ): Promise<HeadingElement[]> {
    const pageStructure = this.getPageStructure(config, integrationConfig);
    
    if (pageStructure) {
      logger.debug(`📋 Using unified DOM structure: ${pageStructure.elements.headings.length} headings found`);
      return pageStructure.elements.headings;
    }

    // Fallback to direct extraction if enabled
    if (integrationConfig.fallbackToDirectExtraction && page) {
      logger.debug('📋 Falling back to direct heading extraction');
      return await this.extractHeadingsDirectly(page);
    }

    return [];
  }

  /**
   * Check if unified DOM structure is available
   */
  hasUnifiedStructure(config: CheckConfig): boolean {
    return !!config.pageStructure;
  }

  /**
   * Get performance metrics from unified structure
   */
  getPerformanceMetrics(config: CheckConfig): { extractionTime: number; elementCounts: Record<string, number> } | null {
    const pageStructure = this.getPageStructure(config, { requiresPageStructure: false, fallbackToDirectExtraction: false, logMissingStructure: false });
    
    if (pageStructure) {
      return {
        extractionTime: pageStructure.performance.extractionTime,
        elementCounts: pageStructure.performance.elementCounts,
      };
    }

    return null;
  }

  /**
   * Get default integration configuration
   */
  private getDefaultConfig(): DOMIntegrationConfig {
    return {
      requiresPageStructure: false,
      fallbackToDirectExtraction: true,
      logMissingStructure: true,
    };
  }

  /**
   * Fallback: Extract images directly from page
   */
  private async extractImagesDirectly(page: Page): Promise<ImageElement[]> {
    return await page.evaluate(() => {
      const images = Array.from(document.querySelectorAll('img'));
      return images.map((img, index) => ({
        selector: img.id ? `#${img.id}` : `img:nth-of-type(${index + 1})`,
        src: img.src || '',
        alt: img.alt || '',
        hasAltText: img.alt.length > 0,
        isDecorative: img.alt === '' || img.getAttribute('role') === 'presentation',
        dimensions: { width: img.naturalWidth || 0, height: img.naturalHeight || 0 },
        accessibility: {
          hasProperAlt: img.alt.length > 2,
          hasLongDesc: !!img.getAttribute('longdesc'),
          hasAriaLabel: !!img.getAttribute('aria-label'),
        },
      }));
    });
  }

  /**
   * Fallback: Extract forms directly from page
   */
  private async extractFormsDirectly(page: Page): Promise<FormElement[]> {
    return await page.evaluate(() => {
      const forms = Array.from(document.querySelectorAll('form'));
      return forms.map((form, index) => ({
        selector: form.id ? `#${form.id}` : `form:nth-of-type(${index + 1})`,
        tagName: form.tagName,
        action: form.action || '',
        method: form.method || 'get',
        hasLabel: !!form.querySelector('legend, h1, h2, h3, h4, h5, h6'),
        fields: [], // Simplified for fallback
        accessibility: {
          hasAccessibleName: !!(form.getAttribute('aria-label') || form.getAttribute('aria-labelledby')),
          hasErrorHandling: !!form.querySelector('[aria-live], [role="alert"]'),
          hasValidation: !!form.querySelector('[aria-invalid], [required]'),
        },
      }));
    });
  }

  /**
   * Fallback: Extract links directly from page
   */
  private async extractLinksDirectly(page: Page): Promise<LinkElement[]> {
    return await page.evaluate(() => {
      const links = Array.from(document.querySelectorAll('a[href]'));
      return links.map((link, index) => ({
        selector: link.id ? `#${link.id}` : `a:nth-of-type(${index + 1})`,
        href: link.href || '',
        text: link.textContent?.trim() || '',
        hasText: (link.textContent?.trim() || '').length > 0,
        isExternal: link.href.startsWith('http') && !link.href.includes(window.location.hostname),
        accessibility: {
          hasAccessibleName: !!(link.textContent?.trim() || link.getAttribute('aria-label') || link.getAttribute('title')),
          hasProperContext: (link.textContent?.trim() || '').length > 3,
          isKeyboardAccessible: link.tabIndex >= 0,
        },
      }));
    });
  }

  /**
   * Fallback: Extract headings directly from page
   */
  private async extractHeadingsDirectly(page: Page): Promise<HeadingElement[]> {
    return await page.evaluate(() => {
      const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'));
      return headings.map((heading, index) => ({
        selector: heading.id ? `#${heading.id}` : `${heading.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
        tagName: heading.tagName,
        level: parseInt(heading.tagName.charAt(1)),
        text: heading.textContent?.trim() || '',
        hasText: (heading.textContent?.trim() || '').length > 0,
        accessibility: {
          hasProperHierarchy: true, // Will be validated later
          isUnique: true, // Simplified for fallback
        },
      }));
    });
  }
}

export default DOMIntegrationHelper;
